# Firebase Authentication Setup

This guide will help you set up Firebase Authentication and Cloud Firestore for the Promptly application.

## Prerequisites

1. A Google account
2. Access to the [Firebase Console](https://console.firebase.google.com/)

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "promptly-ai")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project dashboard, click on "Authentication" in the left sidebar
2. Click on the "Get started" button
3. Go to the "Sign-in method" tab
4. Click on "Email/Password"
5. Enable "Email/Password" authentication
6. Click "Save"

## Step 3: Enable Cloud Firestore

1. In your Firebase project dashboard, click on "Firestore Database" in the left sidebar
2. Click "Create database"
3. <PERSON>ose "Start in test mode" (you can configure security rules later)
4. Select a location for your database (choose the one closest to your users)
5. Click "Done"

## Step 4: Get Firebase Configuration

1. In your Firebase project dashboard, click on the gear icon (⚙️) next to "Project Overview"
2. Select "Project settings"
3. Scroll down to the "Your apps" section
4. Click on the web icon (`</>`) to add a web app
5. Enter an app nickname (e.g., "promptly-web")
6. Click "Register app"
7. Copy the Firebase configuration object

## Step 5: Configure Environment Variables

1. Create a `.env.local` file in your project root (copy from `.env.local.example`)
2. Add your Firebase configuration values:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

## Step 6: Test the Implementation

1. Start your development server: `npm run dev`
2. Navigate to the sign-up page: `http://localhost:3000/SignUpPage`
3. Try creating a new account
4. Check the Firebase Console > Authentication > Users to see if the user was created
5. Check the Firebase Console > Firestore Database > Data to see the user document
6. Test the sign-in functionality

## Features Implemented

### Sign-Up Page
- ✅ Form validation (email format, password strength, full name)
- ✅ Firebase user creation with `createUserWithEmailAndPassword`
- ✅ User profile update with display name
- ✅ **Firestore user document creation with full name and email**
- ✅ Error handling with user-friendly messages
- ✅ Loading states and disabled form during submission
- ✅ Automatic redirect to sign-in page on success
- ✅ Real-time form validation feedback

### Sign-In Page
- ✅ Form validation (email format, password required)
- ✅ Firebase authentication with `signInWithEmailAndPassword`
- ✅ Error handling with user-friendly messages
- ✅ Loading states and disabled form during submission
- ✅ Success message display from sign-up redirect
- ✅ Automatic redirect to home page on successful sign-in

### Authentication Context
- ✅ React context for global authentication state
- ✅ Real-time authentication state monitoring
- ✅ Custom hook for easy access to auth state

### Error Handling
- ✅ Comprehensive Firebase error code mapping
- ✅ User-friendly error messages
- ✅ Field-specific validation errors
- ✅ Network error handling

### Firestore Integration
- ✅ **User document creation in `users` collection**
- ✅ **Automatic document creation on sign-up**
- ✅ **Stores user UID, full name, email, tier, and timestamps**
- ✅ **Default "Starter" tier for new users**
- ✅ **Firestore utilities for CRUD operations**
- ✅ **Error handling for Firestore operations**

## Security Features

- Password requirements: minimum 6 characters with uppercase, lowercase, and numbers
- Email validation
- Terms of service agreement requirement
- Secure Firebase authentication
- Environment variable protection for sensitive config

## Firestore Document Structure

When a user signs up, a document is created in the `users` collection with the following structure:

```typescript
{
  uid: "firebase_user_uid",           // Firebase Auth UID
  fullName: "John Doe",              // User's full name
  email: "<EMAIL>",         // User's email
  tier: "Starter",                   // User's subscription tier (default: "Starter")
  createdAt: Timestamp,              // Account creation time
  updatedAt: Timestamp               // Last update time
}
```

## User Tier System

The application includes a built-in tier system for managing user subscriptions and access levels:

### Available Tiers
- **Starter** (Default) - Basic access
- **Pro** - Enhanced features
- **Business** - Advanced features
- **Premium** - Full access

### Tier Management Functions

```typescript
import { updateUserTier, hasAccessToTier, USER_TIERS } from '@/lib/firestore'

// Update a user's tier
await updateUserTier(userId, USER_TIERS.PRO)

// Check if user has access to a feature
const canAccessFeature = hasAccessToTier(user.tier, USER_TIERS.PRO)
```

### Usage Example

```typescript
// In your components
import { useAuth } from '@/lib/auth-context'
import { USER_TIERS, hasAccessToTier } from '@/lib/firestore'

function PremiumFeature() {
  const { userDocument } = useAuth()

  if (!userDocument || !hasAccessToTier(userDocument.tier, USER_TIERS.PRO)) {
    return <div>Upgrade to Pro to access this feature</div>
  }

  return <div>Premium feature content</div>
}
```

## Next Steps

After setting up Firebase Authentication and Firestore, you may want to:

1. Add password reset functionality
2. Implement email verification
3. Add social authentication (Google, GitHub, etc.)
4. **Add more user profile fields (avatar, preferences, etc.)**
5. **Implement user profile editing functionality**
6. **Add user settings and preferences**
7. Implement role-based access control
8. **Set up Firestore security rules**
